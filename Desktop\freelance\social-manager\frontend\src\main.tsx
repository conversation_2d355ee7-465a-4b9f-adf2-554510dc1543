import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { <PERSON>rowserRouter } from 'react-router-dom'
import { Provider } from 'react-redux'
import { Toaster } from 'react-hot-toast'
import './index.css'
import App from './App'
import { store } from './store/store'

// Debug: Check if root element exists
const rootElement = document.getElementById('root')
console.log('Root element:', rootElement)

if (!rootElement) {
  console.error('Root element not found!')
  document.body.innerHTML = '<div style="padding: 20px; text-align: center;"><h1>SocialHub Pro</h1><p>Root element not found. Please check the HTML.</p></div>'
} else {
  try {
    console.log('Creating React root...')
    const root = createRoot(rootElement)

    console.log('Rendering React app...')
    root.render(
      <StrictMode>
        <Provider store={store}>
          <BrowserRouter>
            <App />
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#363636',
                  color: '#fff',
                },
              }}
            />
          </BrowserRouter>
        </Provider>
      </StrictMode>,
    )
    console.log('React app rendered successfully!')
  } catch (error) {
    console.error('Error rendering React app:', error)
    rootElement.innerHTML = '<div style="padding: 20px; text-align: center;"><h1>SocialHub Pro</h1><p>Error loading application. Please check the console.</p></div>'
  }
}
